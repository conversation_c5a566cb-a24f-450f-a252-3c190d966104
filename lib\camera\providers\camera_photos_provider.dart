import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/slug_ids_map_notifier.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

final class CameraPhotoIdsNotifier extends SlugIdsMapNotifier {}

final cameraPhotoIdsProvider =
    NotifierProvider.autoDispose<
      CameraPhotoIdsNotifier,
      Map<String, List<int>>
    >(CameraPhotoIdsNotifier.new);

final cameraPhotosProvider = Provider.autoDispose
    .family<List<PhotoData>, String>((ref, cameraSlug) {
      final idsMap = ref.watch(cameraPhotoIdsProvider);
      final store = ref.watch(photoStoreProvider);
      final items = <PhotoData>[];

      final ids = idsMap[cameraSlug];
      if (ids == null || ids.isEmpty) return items;

      for (final id in ids) {
        final item = store[id];
        if (item != null) {
          items.add(item);
        }
      }

      return items;
    });

/// High-level service for managing camera photos's reactivity.
/// This is the recommended way to manage camera photos.
final class CameraPhotosReactiveService {
  const CameraPhotosReactiveService(this.ref);

  final Ref ref;

  /// Get camera photos list for a specific camera
  List<PhotoData> getAll(String cameraSlug) {
    return ref.read(cameraPhotosProvider(cameraSlug));
  }

  /// Get camera photo IDs for a specific camera
  List<int> getAllIds(String cameraSlug) {
    return ref.read(cameraPhotoIdsProvider.notifier).getIds(cameraSlug) ?? [];
  }

  /// Get all camera photo IDs across all cameras
  Map<String, List<int>> getAllIdsMap() {
    return ref.read(cameraPhotoIdsProvider);
  }

  /// Add a new camera photo (adds to both global store and camera photos list)
  void addItem(String cameraSlug, PhotoData photo) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItem(photo);

    // Add to camera photos list
    ref.read(cameraPhotoIdsProvider.notifier).addItem(cameraSlug, photo.id);
  }

  /// Add multiple camera photos
  void addItems(String cameraSlug, List<PhotoData> photos) {
    if (photos.isEmpty) return;

    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Add to camera photos list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(cameraPhotoIdsProvider.notifier).addItems(cameraSlug, photoIds);
  }

  /// Remove a camera photo from a specific camera
  void removeFromCamera(String cameraSlug, int photoId) {
    // Remove from camera photos list
    ref
        .read(cameraPhotoIdsProvider.notifier)
        .removeIdFromSlug(cameraSlug, photoId);
  }

  /// Remove a photo from all cameras that contain it
  void removeFromAllCameras(int photoId) {
    ref.read(cameraPhotoIdsProvider.notifier).removeIdFromAllSlugs(photoId);
  }

  /// Remove a camera entirely
  void removeCamera(String cameraSlug) {
    // Remove camera from camera photos list
    ref.read(cameraPhotoIdsProvider.notifier).removeItem(cameraSlug);
  }

  /// Remove multiple cameras
  void removeCameras(List<String> cameraSlugs) {
    if (cameraSlugs.isEmpty) return;

    // Remove cameras from camera photos list
    ref.read(cameraPhotoIdsProvider.notifier).removeItems(cameraSlugs);
  }

  /// Update a photo in the store (automatically reflects in camera photos list)
  void updateItem(PhotoData updatedPhoto) {
    ref.read(photoStoreProvider.notifier).updateItem(updatedPhoto);
  }

  /// Update multiple photos in the store
  void updateItems(List<PhotoData> updatedPhotos) {
    ref.read(photoStoreProvider.notifier).updateItems(updatedPhotos);
  }

  /// Replace all camera photos for a specific camera
  void replaceAll(String cameraSlug, List<PhotoData> photos) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Replace camera photos list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(cameraPhotoIdsProvider.notifier).upsertItems(cameraSlug, photoIds);
  }

  /// Clear all camera photos
  void clear() {
    // Clear the camera photos list
    ref.read(cameraPhotoIdsProvider.notifier).clear();
  }

  /// Clear photos for a specific camera
  void clearCamera(String cameraSlug) {
    // Clear the camera photos list for specific camera
    ref.read(cameraPhotoIdsProvider.notifier).removeItem(cameraSlug);
  }

  /// Get total number of cameras
  int get cameraCount => ref.read(cameraPhotoIdsProvider.notifier).length;

  /// Get camera photos count for a specific camera
  int getCameraPhotoCount(String cameraSlug) =>
      ref.read(cameraPhotoIdsProvider.notifier).getIdsCount(cameraSlug);

  /// Get total photos count across all cameras
  int get totalPhotoCount =>
      ref.read(cameraPhotoIdsProvider.notifier).totalIdsCount;

  /// Check if camera photos list is empty
  bool get isEmpty => ref.read(cameraPhotoIdsProvider.notifier).isEmpty;

  /// Check if camera photos list is not empty
  bool get isNotEmpty => ref.read(cameraPhotoIdsProvider.notifier).isNotEmpty;

  /// Check if a specific camera has photos
  bool hasCamera(String cameraSlug) =>
      ref.read(cameraPhotoIdsProvider.notifier).hasItem(cameraSlug);

  /// Get all camera slugs
  List<String> get allCameraSlugs =>
      ref.read(cameraPhotoIdsProvider.notifier).slugs;
}

/// Provider for the CameraPhotosReactiveService.
final cameraPhotosReactiveServiceProvider =
    Provider.autoDispose<CameraPhotosReactiveService>((ref) {
      return CameraPhotosReactiveService(ref);
    });
